<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced CompanySelector Demo</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --primary: #3b82f6;
            --primary-foreground: #ffffff;
            --secondary: #f1f5f9;
            --secondary-foreground: #0f172a;
            --background: #ffffff;
            --foreground: #0f172a;
            --card: #ffffff;
            --card-foreground: #0f172a;
            --popover: #ffffff;
            --popover-foreground: #0f172a;
            --muted: #f1f5f9;
            --muted-foreground: #64748b;
            --accent: #f1f5f9;
            --accent-foreground: #0f172a;
            --destructive: #ef4444;
            --destructive-foreground: #ffffff;
            --border: #e2e8f0;
            --input: #e2e8f0;
            --ring: #3b82f6;
            --text: #0f172a;
            --hover: #f8fafc;
            --shadow: rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --primary: #60a5fa;
            --primary-foreground: #1e293b;
            --secondary: #1e293b;
            --secondary-foreground: #f8fafc;
            --background: #0f172a;
            --foreground: #f8fafc;
            --card: #1e293b;
            --card-foreground: #f8fafc;
            --popover: #1e293b;
            --popover-foreground: #f8fafc;
            --muted: #334155;
            --muted-foreground: #94a3b8;
            --accent: #334155;
            --accent-foreground: #f8fafc;
            --destructive: #ef4444;
            --destructive-foreground: #ffffff;
            --border: #334155;
            --input: #334155;
            --ring: #60a5fa;
            --text: #f8fafc;
            --hover: #334155;
            --shadow: rgba(0, 0, 0, 0.3);
        }

        body {
            background-color: var(--background);
            color: var(--text);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .demo-card {
            background-color: var(--card);
            border: 1px solid var(--border);
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px var(--shadow);
        }

        .demo-button {
            background-color: var(--primary);
            color: var(--primary-foreground);
            padding: 8px 16px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .demo-button:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Mock theme store
        const useThemeStore = () => {
            const [theme, setTheme] = useState('light');
            
            const colors = {
                primary: 'var(--primary)',
                primaryForeground: 'var(--primary-foreground)',
                background: 'var(--background)',
                text: 'var(--text)',
                mutedForeground: 'var(--muted-foreground)',
                card: 'var(--card)',
                border: 'var(--border)',
                hover: 'var(--hover)',
                muted: 'var(--muted)',
                ring: 'var(--ring)',
            };

            const toggleTheme = () => {
                const newTheme = theme === 'light' ? 'dark' : 'light';
                setTheme(newTheme);
                document.documentElement.setAttribute('data-theme', newTheme);
            };

            return { theme, colors, toggleTheme };
        };

        // Mock company store
        const useCompanyStore = () => {
            const [currentCompany, setCurrentCompany] = useState({
                id: '1',
                name: 'Acme Corporation',
                logo: '🏢',
                domain: 'acme.com',
                isActive: true
            });

            const companies = [
                {
                    id: '1',
                    name: 'Acme Corporation',
                    logo: '🏢',
                    domain: 'acme.com',
                    isActive: true
                },
                {
                    id: '2',
                    name: 'TechStart Inc',
                    logo: '🚀',
                    domain: 'techstart.io',
                    isActive: true
                },
                {
                    id: '3',
                    name: 'Global Solutions',
                    logo: '🌍',
                    domain: 'globalsolutions.com',
                    isActive: true
                }
            ];

            const switchCompany = (companyId) => {
                const company = companies.find(c => c.id === companyId);
                if (company) {
                    setCurrentCompany(company);
                }
            };

            return { companies, currentCompany, switchCompany };
        };

        // Simple icons
        const BuildingIcon = ({ className }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4m0 0v-3.5a2 2 0 011.5-1.5h1a2 2 0 011.5 1.5V21" />
            </svg>
        );

        const ChevronDownIcon = ({ className }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
        );

        const CheckIcon = ({ className }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
        );

        const PlusIcon = ({ className }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
        );

        const SwitchIcon = ({ className }) => (
            <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
        );

        // Simple Dropdown component
        const Dropdown = ({ trigger, items, align = 'left', dropdownClassName = '' }) => {
            const [isOpen, setIsOpen] = useState(false);
            const { colors } = useThemeStore();

            return (
                <div className="relative inline-block">
                    <div onClick={() => setIsOpen(!isOpen)}>
                        {trigger}
                    </div>
                    
                    {isOpen && (
                        <>
                            <div 
                                className="fixed inset-0 z-10" 
                                onClick={() => setIsOpen(false)}
                            />
                            <div 
                                className={`absolute z-20 mt-2 py-2 rounded-lg shadow-lg ${dropdownClassName} ${align === 'right' ? 'right-0' : 'left-0'}`}
                                style={{ 
                                    backgroundColor: colors.card,
                                    border: `1px solid ${colors.border}`,
                                    boxShadow: `0 10px 25px ${colors.shadow}`,
                                }}
                            >
                                {items.map((item) => {
                                    if (item.isDivider) {
                                        return (
                                            <div 
                                                key={item.id}
                                                className="my-1 h-px"
                                                style={{ backgroundColor: colors.border }}
                                            />
                                        );
                                    }
                                    
                                    return (
                                        <div
                                            key={item.id}
                                            className="px-4 py-2 cursor-pointer transition-colors duration-150 hover:bg-opacity-50"
                                            style={{ 
                                                ':hover': { backgroundColor: colors.hover }
                                            }}
                                            onClick={() => {
                                                item.onClick?.();
                                                setIsOpen(false);
                                            }}
                                            onMouseEnter={(e) => {
                                                e.target.style.backgroundColor = colors.hover;
                                            }}
                                            onMouseLeave={(e) => {
                                                e.target.style.backgroundColor = 'transparent';
                                            }}
                                        >
                                            <div className="flex items-center space-x-3">
                                                {item.icon}
                                                <div className="flex-1">
                                                    <div className="text-sm font-medium">{item.label}</div>
                                                    {item.description && (
                                                        <div 
                                                            className="text-xs"
                                                            style={{ color: colors.mutedForeground }}
                                                        >
                                                            {item.description}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </>
                    )}
                </div>
            );
        };

        // Enhanced CompanySelector component (simplified version)
        const CompanySelector = ({ 
            showLabel = false, 
            variant = 'default', 
            size = 'md',
            showCompanyCount = false,
            className = '' 
        }) => {
            const { colors } = useThemeStore();
            const { companies, currentCompany, switchCompany } = useCompanyStore();
            const [isHovered, setIsHovered] = useState(false);

            const sizeConfig = {
                sm: { padding: 'px-2 py-1.5', iconSize: 'w-4 h-4', logoSize: 'w-6 h-6', textSize: 'text-xs' },
                md: { padding: 'px-3 py-2', iconSize: 'w-4 h-4', logoSize: 'w-7 h-7', textSize: 'text-sm' },
                lg: { padding: 'px-4 py-3', iconSize: 'w-5 h-5', logoSize: 'w-8 h-8', textSize: 'text-base' },
            };

            const config = sizeConfig[size];

            const companyItems = companies.map(company => ({
                id: company.id,
                label: company.name,
                icon: (
                    <div className="flex items-center justify-between w-full">
                        <div className="flex items-center space-x-3">
                            <div 
                                className={`${config.logoSize} rounded-lg flex items-center justify-center overflow-hidden`}
                                style={{ 
                                    backgroundColor: currentCompany?.id === company.id ? `${colors.primary}15` : colors.muted,
                                }}
                            >
                                {company.logo ? (
                                    <div className="text-lg leading-none">{company.logo}</div>
                                ) : (
                                    <div style={{ color: colors.mutedForeground }}>
                                        <BuildingIcon className={config.iconSize} />
                                    </div>
                                )}
                            </div>
                            <div className="flex flex-col min-w-0 flex-1">
                                <div className="font-medium text-sm truncate">{company.name}</div>
                                {company.domain && (
                                    <div 
                                        className="text-xs truncate"
                                        style={{ color: colors.mutedForeground }}
                                    >
                                        {company.domain}
                                    </div>
                                )}
                            </div>
                        </div>
                        {currentCompany?.id === company.id && (
                            <div style={{ color: colors.primary }}>
                                <CheckIcon className="w-4 h-4" />
                            </div>
                        )}
                    </div>
                ),
                onClick: () => switchCompany(company.id),
            }));

            const managementItems = [
                { id: 'divider-1', label: '', isDivider: true },
                {
                    id: 'add-company',
                    label: 'Add New Company',
                    icon: (
                        <div className="flex items-center space-x-3">
                            <div 
                                className={`${config.logoSize} rounded-lg flex items-center justify-center`}
                                style={{ backgroundColor: `${colors.primary}15` }}
                            >
                                <div style={{ color: colors.primary }}>
                                    <PlusIcon className={config.iconSize} />
                                </div>
                            </div>
                        </div>
                    ),
                    onClick: () => console.log('Add company'),
                    description: 'Create a new company profile',
                },
                {
                    id: 'manage-companies',
                    label: 'Manage Companies',
                    icon: (
                        <div className="flex items-center space-x-3">
                            <div 
                                className={`${config.logoSize} rounded-lg flex items-center justify-center`}
                                style={{ backgroundColor: colors.muted }}
                            >
                                <div style={{ color: colors.mutedForeground }}>
                                    <SwitchIcon className={config.iconSize} />
                                </div>
                            </div>
                        </div>
                    ),
                    onClick: () => console.log('Manage companies'),
                    description: 'Edit company settings and permissions',
                },
            ];

            const dropdownItems = [...companyItems, ...managementItems];

            const trigger = (
                <div
                    className={`
                        flex items-center space-x-2 ${config.padding} rounded-lg 
                        transition-all duration-200 cursor-pointer group
                        ${variant === 'compact' ? 'min-w-0' : 'min-w-[120px]'}
                        ${className}
                    `}
                    style={{ 
                        backgroundColor: isHovered ? `${colors.hover}40` : 'transparent',
                        border: `1px solid ${isHovered ? colors.ring : 'transparent'}`,
                    }}
                    onMouseEnter={() => setIsHovered(true)}
                    onMouseLeave={() => setIsHovered(false)}
                >
                    <div className="flex-shrink-0 relative">
                        <div 
                            className={`${config.logoSize} rounded-lg flex items-center justify-center overflow-hidden transition-all duration-200`}
                            style={{ 
                                backgroundColor: currentCompany?.logo ? `${colors.primary}10` : colors.muted,
                            }}
                        >
                            {currentCompany?.logo ? (
                                <div className="text-lg leading-none">{currentCompany.logo}</div>
                            ) : (
                                <div style={{ color: isHovered ? colors.primary : colors.text }}>
                                    <BuildingIcon className={config.iconSize} />
                                </div>
                            )}
                        </div>
                        {currentCompany && (
                            <div 
                                className="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2"
                                style={{ 
                                    backgroundColor: colors.primary,
                                    borderColor: colors.background,
                                }}
                            />
                        )}
                    </div>

                    {(showLabel || variant === 'expanded') && currentCompany && (
                        <div className="flex-1 min-w-0">
                            <div
                                className={`${config.textSize} font-medium truncate transition-colors duration-200`}
                                style={{ color: isHovered ? colors.primary : colors.text }}
                            >
                                {currentCompany.name}
                            </div>
                            {variant === 'expanded' && currentCompany.domain && (
                                <div
                                    className="text-xs truncate"
                                    style={{ color: colors.mutedForeground }}
                                >
                                    {currentCompany.domain}
                                </div>
                            )}
                            {showCompanyCount && companies.length > 1 && (
                                <div
                                    className="text-xs"
                                    style={{ color: colors.mutedForeground }}
                                >
                                    {companies.length} companies
                                </div>
                            )}
                        </div>
                    )}

                    <div 
                        style={{ 
                            color: isHovered ? colors.primary : colors.mutedForeground,
                        }}
                    >
                        <ChevronDownIcon className="w-4 h-4 transition-all duration-200" />
                    </div>
                </div>
            );

            return (
                <Dropdown
                    trigger={trigger}
                    items={dropdownItems}
                    align="left"
                    dropdownClassName="min-w-[280px]"
                />
            );
        };

        // Demo App
        const App = () => {
            const { colors, theme, toggleTheme } = useThemeStore();

            return (
                <div className="min-h-screen p-8" style={{ backgroundColor: colors.background }}>
                    <div className="max-w-4xl mx-auto space-y-8">
                        <div className="text-center space-y-4">
                            <h1 className="text-3xl font-bold" style={{ color: colors.text }}>
                                Enhanced CompanySelector Component
                            </h1>
                            <p className="text-lg" style={{ color: colors.mutedForeground }}>
                                Showcasing the improved UI/UX with modern design patterns
                            </p>
                            <button
                                className="demo-button"
                                onClick={toggleTheme}
                            >
                                Switch to {theme === 'light' ? 'Dark' : 'Light'} Theme
                            </button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <div className="demo-card">
                                <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                                    Default Variant
                                </h3>
                                <div className="space-y-4">
                                    <CompanySelector />
                                    <CompanySelector showLabel={true} />
                                    <CompanySelector showLabel={true} showCompanyCount={true} />
                                </div>
                            </div>

                            <div className="demo-card">
                                <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                                    Compact Variant
                                </h3>
                                <div className="space-y-4">
                                    <CompanySelector variant="compact" />
                                    <CompanySelector variant="compact" showLabel={true} />
                                </div>
                            </div>

                            <div className="demo-card">
                                <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                                    Expanded Variant
                                </h3>
                                <div className="space-y-4">
                                    <CompanySelector variant="expanded" />
                                </div>
                            </div>

                            <div className="demo-card">
                                <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                                    Size Variants
                                </h3>
                                <div className="space-y-4">
                                    <div>
                                        <p className="text-sm mb-2" style={{ color: colors.mutedForeground }}>Small</p>
                                        <CompanySelector size="sm" showLabel={true} />
                                    </div>
                                    <div>
                                        <p className="text-sm mb-2" style={{ color: colors.mutedForeground }}>Medium</p>
                                        <CompanySelector size="md" showLabel={true} />
                                    </div>
                                    <div>
                                        <p className="text-sm mb-2" style={{ color: colors.mutedForeground }}>Large</p>
                                        <CompanySelector size="lg" showLabel={true} />
                                    </div>
                                </div>
                            </div>

                            <div className="demo-card">
                                <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                                    Interactive Features
                                </h3>
                                <div className="space-y-4">
                                    <CompanySelector showLabel={true} />
                                    <CompanySelector showLabel={true} showCompanyCount={true} />
                                </div>
                            </div>

                            <div className="demo-card">
                                <h3 className="text-lg font-semibold mb-4" style={{ color: colors.text }}>
                                    Custom Styling
                                </h3>
                                <div className="space-y-4">
                                    <CompanySelector 
                                        className="w-full"
                                        showLabel={true}
                                        variant="expanded"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>