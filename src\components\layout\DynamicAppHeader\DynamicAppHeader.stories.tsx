import type { <PERSON>a, StoryObj } from '@storybook/react-vite';
import DynamicAppHeader from './DynamicAppHeader';
import { LightningIcon, BellIcon, ListIcon, GridIcon, BarChartIcon } from '../../icons';

// Sample icons for the story
const AppIcon = () => (
  <LightningIcon className="w-8 h-8" />
);

const UserAvatar = () => (
  <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
    JD
  </div>
);



const meta: Meta<typeof DynamicAppHeader> = {
  title: 'Layout/DynamicAppHeader',
  component: DynamicAppHeader,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'A comprehensive, responsive application header component with two-row layout. Features global navigation, contextual controls, search functionality, and mobile-optimized responsive behavior.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    app: {
      description:
        'Application configuration including name, icon, and navigation links',
    },
    user: {
      description: 'User information including name, avatar, and notifications',
    },
    view: {
      description:
        'View-specific configuration including title, actions, search, pagination, and view modes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for stories
const sampleAppData = {
  name: 'Sales',
  icon: <AppIcon />,
  navLinks: [
    { label: 'Orders', href: '/orders', isActive: false },
    { label: 'Products', href: '/products', isActive: false },
    { label: 'Quotations', href: '/quotations', isActive: true },
    { label: 'Invoices', href: '/invoices', isActive: false },
    { label: 'Reporting', href: '/reporting', isActive: false },
    { label: 'Configuration', href: '/configuration', isActive: false },
  ],
};

const sampleUserData = {
  name: 'John Doe',
  avatar: <UserAvatar />,
  notifications: [{ count: 3, icon: <BellIcon /> }],
};

const sampleViewData = {
  title: 'Quotations',
  actions: [
    {
      label: 'New',
      onClick: () => console.log('New clicked'),
      isPrimary: true,
    },
    {
      label: 'Upload',
      onClick: () => console.log('Upload clicked'),
      isPrimary: false,
    },
  ],
  search: {
    filters: [
      { id: 'undelivered', label: 'Undelivered' },
      { id: 'complete', label: 'Complete' },
    ],
    onSearch: (query: string) => console.log('Search:', query),
    onRemoveFilter: (id: any) => console.log('Remove filter:', id),
  },
  pagination: {
    currentRange: '1-80 / 150',
    onNext: () => console.log('Next page'),
    onPrev: () => console.log('Previous page'),
  },
  viewModes: [
    { name: 'List', icon: <ListIcon /> },
    { name: 'Kanban', icon: <GridIcon /> },
    { name: 'Graph', icon: <BarChartIcon /> },
  ],
  activeViewMode: 'List',
};

export const Default: Story = {
  args: {
    app: sampleAppData,
    user: sampleUserData,
    view: sampleViewData,
  },
};

export const WithoutFilters: Story = {
  args: {
    app: sampleAppData,
    user: sampleUserData,
    view: {
      ...sampleViewData,
      search: {
        ...sampleViewData.search,
        filters: [],
      },
    },
  },
};

export const DifferentActiveView: Story = {
  args: {
    app: sampleAppData,
    user: sampleUserData,
    view: {
      ...sampleViewData,
      activeViewMode: 'Kanban',
    },
  },
};

export const MultipleNotifications: Story = {
  args: {
    app: sampleAppData,
    user: {
      ...sampleUserData,
      notifications: [
        { count: 5, icon: <BellIcon /> },
        { count: 12, icon: <BellIcon /> },
        { count: 99, icon: <BellIcon /> },
      ],
    },
    view: sampleViewData,
  },
};

export const LongAppName: Story = {
  args: {
    app: {
      ...sampleAppData,
      name: 'Enterprise Resource Planning',
    },
    user: sampleUserData,
    view: {
      ...sampleViewData,
      title: 'Customer Relationship Management',
    },
  },
};
