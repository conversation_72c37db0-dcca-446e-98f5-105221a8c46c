import React from 'react';
import CompanySelector from './CompanySelector';
import { useThemeStore } from '../../../stores/themeStore';

const CompanySelectorDemo: React.FC = () => {
  const { colors, theme, toggleTheme } = useThemeStore();

  return (
    <div 
      className="min-h-screen p-8 transition-colors duration-300"
      style={{ backgroundColor: colors.background }}
    >
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 
            className="text-3xl font-bold"
            style={{ color: colors.text }}
          >
            Enhanced CompanySelector Component
          </h1>
          <p 
            className="text-lg"
            style={{ color: colors.mutedForeground }}
          >
            Showcasing the improved UI/UX with modern design patterns
          </p>
          
          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            className="px-4 py-2 rounded-lg transition-all duration-200 hover:scale-105"
            style={{ 
              backgroundColor: colors.primary,
              color: colors.primaryForeground,
            }}
          >
            Switch to {theme === 'light' ? 'Dark' : 'Light'} Theme
          </button>
        </div>

        {/* Demo Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          {/* Default Variant */}
          <div 
            className="p-6 rounded-xl border"
            style={{ 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: colors.text }}
            >
              Default Variant
            </h3>
            <div className="space-y-4">
              <CompanySelector />
              <CompanySelector showLabel={true} />
              <CompanySelector showLabel={true} showCompanyCount={true} />
            </div>
          </div>

          {/* Compact Variant */}
          <div 
            className="p-6 rounded-xl border"
            style={{ 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: colors.text }}
            >
              Compact Variant
            </h3>
            <div className="space-y-4">
              <CompanySelector variant="compact" />
              <CompanySelector variant="compact" showLabel={true} />
            </div>
          </div>

          {/* Expanded Variant */}
          <div 
            className="p-6 rounded-xl border"
            style={{ 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: colors.text }}
            >
              Expanded Variant
            </h3>
            <div className="space-y-4">
              <CompanySelector variant="expanded" />
            </div>
          </div>

          {/* Size Variants */}
          <div 
            className="p-6 rounded-xl border"
            style={{ 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: colors.text }}
            >
              Size Variants
            </h3>
            <div className="space-y-4">
              <div>
                <p 
                  className="text-sm mb-2"
                  style={{ color: colors.mutedForeground }}
                >
                  Small
                </p>
                <CompanySelector size="sm" showLabel={true} />
              </div>
              <div>
                <p 
                  className="text-sm mb-2"
                  style={{ color: colors.mutedForeground }}
                >
                  Medium (Default)
                </p>
                <CompanySelector size="md" showLabel={true} />
              </div>
              <div>
                <p 
                  className="text-sm mb-2"
                  style={{ color: colors.mutedForeground }}
                >
                  Large
                </p>
                <CompanySelector size="lg" showLabel={true} />
              </div>
            </div>
          </div>

          {/* Interactive States */}
          <div 
            className="p-6 rounded-xl border"
            style={{ 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: colors.text }}
            >
              Interactive Features
            </h3>
            <div className="space-y-4">
              <div>
                <p 
                  className="text-sm mb-2"
                  style={{ color: colors.mutedForeground }}
                >
                  Hover effects and animations
                </p>
                <CompanySelector showLabel={true} />
              </div>
              <div>
                <p 
                  className="text-sm mb-2"
                  style={{ color: colors.mutedForeground }}
                >
                  With company count
                </p>
                <CompanySelector showLabel={true} showCompanyCount={true} />
              </div>
            </div>
          </div>

          {/* Custom Styling */}
          <div 
            className="p-6 rounded-xl border"
            style={{ 
              backgroundColor: colors.card,
              borderColor: colors.border,
            }}
          >
            <h3 
              className="text-lg font-semibold mb-4"
              style={{ color: colors.text }}
            >
              Custom Styling
            </h3>
            <div className="space-y-4">
              <CompanySelector 
                className="w-full"
                showLabel={true}
                variant="expanded"
              />
            </div>
          </div>
        </div>

        {/* Features List */}
        <div 
          className="p-6 rounded-xl border"
          style={{ 
            backgroundColor: colors.card,
            borderColor: colors.border,
          }}
        >
          <h3 
            className="text-xl font-semibold mb-4"
            style={{ color: colors.text }}
          >
            Enhanced Features
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ul className="space-y-2">
              <li 
                className="flex items-center space-x-2"
                style={{ color: colors.text }}
              >
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
                <span>Multiple size variants (sm, md, lg)</span>
              </li>
              <li 
                className="flex items-center space-x-2"
                style={{ color: colors.text }}
              >
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
                <span>Three display variants (default, compact, expanded)</span>
              </li>
              <li 
                className="flex items-center space-x-2"
                style={{ color: colors.text }}
              >
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
                <span>Enhanced hover states and animations</span>
              </li>
              <li 
                className="flex items-center space-x-2"
                style={{ color: colors.text }}
              >
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
                <span>Improved visual hierarchy</span>
              </li>
            </ul>
            <ul className="space-y-2">
              <li 
                className="flex items-center space-x-2"
                style={{ color: colors.text }}
              >
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
                <span>Better accessibility with ARIA labels</span>
              </li>
              <li 
                className="flex items-center space-x-2"
                style={{ color: colors.text }}
              >
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
                <span>Active company indicators</span>
              </li>
              <li 
                className="flex items-center space-x-2"
                style={{ color: colors.text }}
              >
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
                <span>Enhanced dropdown items with descriptions</span>
              </li>
              <li 
                className="flex items-center space-x-2"
                style={{ color: colors.text }}
              >
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
                <span>Consistent theming integration</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanySelectorDemo;